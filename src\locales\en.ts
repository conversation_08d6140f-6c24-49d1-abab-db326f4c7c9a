export default {
  common: {
    clear: 'Clear',
    copy: 'Copy',
    close: 'Close',
    download: 'Download',
    loadExample: 'Load Example',
    selectAll: 'Select All',
    clearSelection: 'Clear Selection',
    extract: 'Extract',
    results: 'Results',
    options: 'Options',
    input: 'Input',
    preview: 'Preview',
    statistics: 'Statistics',
    fields: 'Fields',
    items: 'items',
    found: 'found',
    extracted: 'extracted',
    with: 'with',
    total: 'Total',
    unique: 'Unique',
    nonEmpty: 'Non-empty',
    loading: 'Loading...',
    remove: 'Remove',
  },
  toast: {
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Info',
    copied: 'Results copied to clipboard!',
    copyFailed: 'Failed to copy to clipboard',
    downloadSuccess: 'File downloaded successfully!',
  },
  footer: {
    madeWith: 'Made with',
    by: 'by',
  },
  categories: {
    webTools: {
      name: 'Web Tools',
      description: 'Tools for web development and analysis',
    },
    jsonTools: {
      name: 'JSON Tools',
      description: 'Comprehensive JSON processing and conversion utilities',
    },
    dataTools: {
      name: 'Data Tools',
      description: 'Tools for data processing and manipulation',
    },
    imageTools: {
      name: 'Image Tools',
      description: 'Tools for image processing and management',
    },
    converters: {
      name: 'Converters',
      description: 'Format conversion utilities',
    },
    generators: {
      name: 'Generators',
      description: 'Code and content generators',
    },
  },
  pagination: {
    previous: 'Previous',
    next: 'Next',
    page: 'Page',
    of: 'of',
  },
  status: {
    active: 'Active',
    'coming-soon': 'Coming Soon',
  },
  navigation: {
    title: 'Professional web development utilities',
    tools: 'Tools',
    language: 'Language',
    categories: 'Tool Categories',
    menu: 'Menu',
    close: 'Close',
    search: 'Search tools...',
    noResults: 'No tools found matching your search.',
    noToolsInCategory: 'No tools available in this category.',
    home: 'Home',
  },
  homepage: {
    title: 'Developer Tools Collection',
    subtitle:
      'Powerful online tools for developers, designers, and content creators. Everything you need to boost your productivity.',
    recommendedTools: 'Recommended Tools',
    exploreCategories: 'Explore Categories',
    stats: {
      totalTools: 'Total Tools',
      activeTools: 'Active Tools',
      categories: 'Categories',
      comingSoon: 'Coming Soon',
    },
  },
  notFound: {
    title: 'Page Not Found',
    description: 'The tool or page you are looking for does not exist or has been moved.',
    backToHome: 'Back to Home',
    goBack: 'Go Back',
    popularTools: 'Popular Tools',
    helpText:
      'If you need help finding a specific tool, please check our categories in the sidebar.',
  },
  tools: {
    htmlExtractor: {
      title: 'HTML Content Extractor',
      description:
        'Extract images, videos, links, and other resources from HTML code with one click',
      contentTypes: 'Content Types',
      baseUrl: 'Base URL',
      inputPlaceholder: 'Paste your HTML code here...',
      extractionResults: 'Extraction Results',
      noResults:
        'No extraction results yet. Please input HTML code and select content types to extract.',
      features: {
        imageExtraction: {
          title: 'Image Extraction',
          description:
            'Automatically extract all image URLs from HTML, including img tags and CSS background images. Supports relative to absolute URL conversion for easy use.',
        },
        mediaProcessing: {
          title: 'Media Processing',
          description:
            'Batch extract video and audio file links, supporting multiple formats (MP4, WebM, Ogg, MP3, etc.). Automatically recognizes source files in video and audio tags.',
        },
        linkAnalysis: {
          title: 'Link Analysis',
          description:
            'Extract all hyperlinks from the page, including href attributes of a tags. Supports filtering internal and external links to help analyze website structure.',
        },
      },
      options: {
        uniqueOnly: 'Unique Results Only',
        absoluteUrls: 'Convert to Absolute URLs',
      },
      types: {
        images: 'Images',
        videos: 'Videos',
        audio: 'Audio',
        links: 'Links',
        css: 'CSS',
        javascript: 'JavaScript',
        iframes: 'iFrames',
        metadata: 'Metadata',
        forms: 'Forms',
      },
    },
    jsonExtractor: {
      title: 'JSON Field Extractor',
      description: 'Extract specific fields from JSON array data with one click',
      availableFields: 'Available Fields',
      inputTitle: 'Input JSON Array',
      inputNote: 'Please paste JSON array data in the format:',
      inputDescription:
        'The tool will automatically parse the JSON and list all available fields for selection.',
      inputPlaceholder: 'Paste your JSON here.',
      extractedData: 'EXTRACTED DATA',
      fieldStatistics: 'FIELD STATISTICS',
      noResults:
        'No extraction results yet. Please input JSON array data and select fields to extract.',
      options: {
        preserveStructure: 'Preserve Object Structure',
        removeEmpty: 'Remove Empty Values',
        flattenNested: 'Flatten Nested Objects',
      },
      features: {
        fieldExtraction: {
          title: 'Field Extraction',
          description:
            'Automatically parse JSON array and extract selected fields. Supports nested objects and preserves data types for accurate extraction.',
        },
        smartFiltering: {
          title: 'Smart Filtering',
          description:
            'Choose specific fields to include in the output. Option to remove empty values and preserve original object structure for clean results.',
        },
        exportOptions: {
          title: 'Export Options',
          description:
            'Copy extracted data to clipboard or download as JSON file. Includes field statistics and data analysis for better understanding of your dataset.',
        },
      },
      errors: {
        invalidFormat: 'Input must be a JSON array in format: [{},{},...]',
        emptyArray: 'JSON array cannot be empty',
        noFields: 'Please select at least one field to extract',
        invalidJson: 'Invalid JSON format:',
        noData: 'Please provide JSON data to extract from',
      },
    },
    jsonKeysExtractor: {
      title: 'JSON Keys/Values Extractor',
      description: 'Extract all unique keys or values from JSON objects and arrays',
      inputTitle: 'Input JSON',
      inputPlaceholder: 'Paste your JSON here...',
      extractedKeys: 'Extracted Keys',
      extractedValues: 'Extracted Values',
      noResults: 'No keys or values extracted yet. Please input JSON to analyze.',
      extractionOptions: 'Extraction Options',
      includeNested: 'Include Nested Keys (with dot notation)',
      sortResults: 'Sort Results Alphabetically',
      includeArrayIndices: 'Include Array Indices',
      outputFormat: 'Output Format',
      formatOptions: {
        array: 'JSON Array',
        list: 'Line-separated List',
        tree: 'Tree Structure',
      },
      modeToggle: {
        keys: 'Extract Keys',
        values: 'Extract Values',
      },
      features: {
        keyDiscovery: {
          title: 'Key Discovery',
          description: 'Automatically discover all keys from complex JSON structures',
        },
        valueExtraction: {
          title: 'Value Extraction',
          description: 'Extract all values from complex JSON structures',
        },
        nestedSupport: {
          title: 'Nested Support',
          description: 'Handle nested objects with path notation for deep structures',
        },
        valueTypes: {
          title: 'Value Types',
          description: 'Extract values of all types (strings, numbers, booleans, etc.)',
        },
        exportOptions: {
          title: 'Multiple Formats',
          description: 'Export as array, list, or tree structure',
        },
      },
      errors: {
        invalidJson: 'Invalid JSON format:',
      },
    },
    imageListProcessor: {
      title: 'Image List Processor',
      description: 'Input a list of image URLs and display them in a visual gallery format',
      inputTitle: 'Input Image URLs',
      inputNote: 'Paste your image URLs below, one per line:',
      inputPlaceholder:
        'Paste image URLs here, one per line...\n\nExample:\nhttps://example.com/image1.jpg\nhttps://example.com/image2.png\nhttps://example.com/image3.webp',
      imagePreview: 'Image Gallery',
      noResults: 'No valid image URLs found. Please enter valid image URLs.',
      imageError: 'Failed to load',
      emptyState: {
        title: 'No images to display',
        description: 'Enter some image URLs above to see them displayed in the gallery below.',
      },
      features: {
        simple: {
          title: 'Simple Input',
          description: 'Just paste image URLs line by line - no complex formatting needed.',
        },
        gallery: {
          title: 'Enhanced Gallery',
          description:
            'View all images in a clean 4-column layout with full-featured lightbox preview supporting zoom, pan, and keyboard navigation.',
        },
        fast: {
          title: 'Professional Preview',
          description:
            'Advanced image viewer with zoom, scroll, drag, and full-screen capabilities for detailed inspection.',
        },
      },
    },
    videoToGifConverter: {
      title: 'Video to GIF Converter',
      description:
        'Convert videos to animated GIFs with customizable text overlays and timing controls',
      howToUse: {
        title: 'How to Use',
        step1: 'Upload a video file by clicking "Select Video File" or dragging and dropping',
        step2: 'Adjust GIF settings (width, quality, frame rate)',
        step3: 'Set the time range for the GIF and add text overlays if desired',
        step4: 'Click "Generate GIF" to create your animated GIF',
      },
      tips: {
        title: 'Tips for Best Results',
        tip1: 'For best results, use short video clips (under 10 seconds)',
        tip2: 'Lower frame rates (10-15 FPS) create smaller file sizes',
        tip3: 'Smaller GIF widths (200-400px) load faster and consume less memory',
        tip4: 'Use medium quality for a good balance between file size and image quality',
      },
      loadingVideo: 'Loading video...',
      upload: {
        title: 'Upload Video',
        dragDrop: 'Drag & drop your video here',
        selectFile: 'Select Video File',
        supportedFormats: 'Supports MP4, AVI, MOV, WebM and other video formats (Max: 100MB)',
      },
      settings: {
        width: 'GIF Width (px)',
        quality: 'Quality',
        fps: 'Frame Rate (FPS)',
        qualityOptions: {
          high: 'High Quality',
          medium: 'Medium Quality',
          low: 'Low Quality (Smaller File)',
        },
      },
      preview: {
        title: 'Video Preview & Controls',
      },
      actions: {
        startCapture: 'Start Capture',
        stopCapture: 'Stop Capture',
        generateGif: 'Generate GIF',
      },
      timeRange: {
        title: 'Time Range Selection',
        start: 'Start',
        end: 'End',
        setStart: 'Set Start',
        setEnd: 'Set End',
      },
      textOverlay: {
        title: 'Text Overlays',
        add: 'Add Text',
        text: 'Text',
        placeholder: 'Enter overlay text...',
        startTime: 'Start Time (s)',
        endTime: 'End Time (s)',
        fontSize: 'Font Size',
        color: 'Color',
        position: 'Position',
        positions: {
          top: 'Top',
          center: 'Center',
          bottom: 'Bottom',
        },
      },
      processing: {
        title: 'Processing Video',
        description: 'Converting your video to GIF with text overlays. This may take a moment...',
        preview: 'Preview',
      },
      result: {
        title: 'Generated GIF',
        download: 'Download GIF',
        createNew: 'Create New GIF',
      },
      features: {
        conversion: {
          title: 'Video Conversion',
          description:
            'Convert videos to high-quality animated GIFs with customizable frame rate and dimensions.',
        },
        textOverlay: {
          title: 'Text Overlays',
          description:
            'Add multiple text overlays with precise timing, custom colors, fonts, and positioning.',
        },
        customization: {
          title: 'Full Customization',
          description:
            'Control every aspect including quality, size, timing, and text appearance for perfect results.',
        },
      },
      errors: {
        invalidFile: 'Please select a valid video file.',
        fileTooLarge: 'File size must be less than 100MB.',
        processingFailed: 'Failed to process video. Please try again.',
        noVideoSelected: 'Please select a video file first.',
        invalidTimeRange: 'Invalid time range. End time must be greater than start time.',
      },
      messages: {
        fileLoaded: 'Video file loaded successfully!',
        gifGenerated: 'GIF generated successfully!',
        filePasted: 'Video file pasted from clipboard!',
      },
    },
    apngGenerator: {
      title: 'APNG Generator',
      description:
        'Create animated PNG files from multiple static images with customizable settings',
      uploadTitle: 'Upload Image Frames',
      uploadDescription:
        'Drag and drop multiple images or click to select frames for your animation',
      selectFiles: 'Select Image Files',
      supportedFormats: 'Supported formats',
      settings: 'Animation Settings',
      frameDelay: 'Frame Delay',
      loopCount: 'Loop Count',
      infinite: 'Infinite',
      outputWidth: 'Output Width',
      outputHeight: 'Output Height',
      advancedOptions: 'Advanced Options',
      maintainAspectRatio: 'Maintain Aspect Ratio',
      optimizeSize: 'Optimize File Size',
      frameList: 'Animation Frames',
      generateAPNG: 'Generate APNG',
      generating: 'Generating...',
      preview: 'Preview Animation',
      animationPreview: 'Animation Preview',
      downloadAPNG: 'Download APNG',
      reorderHint:
        'Frames will be animated in the order shown above. You can remove unwanted frames by clicking the × button.',
      features: {
        title: 'Key Features',
        highQuality: {
          title: 'High Quality Output',
          description:
            'Generate lossless animated PNG files with support for transparency and 24-bit color',
        },
        customizable: {
          title: 'Fully Customizable',
          description: 'Control frame timing, loop count, dimensions, and optimization settings',
        },
        easyToUse: {
          title: 'Easy to Use',
          description: 'Simple drag-and-drop interface with real-time preview and instant download',
        },
      },
    },
    backgroundRemover: {
      title: 'Background Remover',
      description: 'Remove backgrounds from images automatically using AI technology',
      features: {
        aiPowered: {
          title: 'AI-Powered',
          description:
            'Advanced machine learning algorithms for precise background detection and removal',
        },
        fastProcessing: {
          title: 'Fast Processing',
          description: 'Quick background removal with high-quality results in seconds',
        },
        highQuality: {
          title: 'High Quality',
          description: 'Preserve image quality and details while removing backgrounds cleanly',
        },
      },
      upload: {
        title: 'Upload Image',
        dragDrop: 'Drag & Drop Image Here',
        supportedFormats: 'Supports JPG, PNG, GIF, and other image formats',
        selectFile: 'Select Image',
      },
      preview: {
        original: 'Original Image',
        originalAlt: 'Original image',
        processed: 'Background Removed',
        processedAlt: 'Processed image with background removed',
      },
      options: {
        title: 'Output Options',
        model: 'AI Model',
        outputFormat: 'Output Format',
        transparent: 'Transparent background',
        whiteBackground: 'White background',
        backgroundColor: 'Background Color',
        quality: 'Output Quality',
      },
      models: {
        small: 'Small (Fast)',
        medium: 'Medium (Balanced)',
        large: 'Large (Best Quality)',
      },
      actions: {
        remove: 'Remove Background',
      },
      processing: {
        inProgress: 'Processing...',
        analyzing: 'Analyzing image and removing background...',
        pleaseWait: 'This may take a few seconds',
      },
      result: {
        title: 'Result',
        noResult: 'No processed image yet. Please upload an image to remove its background.',
        complete: 'Background Removal Complete',
        ready: 'Your image is ready for download',
      },
      imageInfo: {
        size: 'File size',
        format: 'Format',
      },
      tips: {
        title: 'Tips for Best Results',
        tip1: 'Use high-resolution images with clear subject boundaries for best results',
        tip2: 'Images with good contrast between subject and background work better',
        tip3: 'Avoid images with complex backgrounds or similar colors to the main subject',
        tip4: 'PNG format preserves transparency, while JPG uses white background',
        tip5: 'Use the comparison view to see before and after results side by side',
      },
      comparison: {
        before: 'Before',
        after: 'After',
      },
    },
    csvtojson: {
      title: 'CSV to JSON Converter',
      description: 'Convert CSV data to JSON format with customizable parsing options',
      introduction: {
        title: 'Tool Introduction',
        description:
          'Online CSV to JSON converter for converting fixed-symbol separated CSV format data to JSON format data.',
        usage:
          'Default delimiter is tab (\\t), but you can change it to comma or other symbols. Supports converting CSV to JSON objects or arrays.',
      },
      example: {
        title: 'Example',
        input: 'CSV Input:',
        output: 'JSON Output:',
      },
      input: {
        title: 'Input CSV Data',
        placeholder:
          'Paste your CSV data here...\n\nExample:\nname,age,score\nLi Hua,25,89\nXiao Ming,22,85',
        fileUpload: 'Upload CSV File',
      },
      options: {
        title: 'Parsing Options',
        delimiter: 'Delimiter',
        outputFormat: 'Output Format',
        hasHeaders: 'First Row as Headers',
        skipEmptyLines: 'Skip Empty Lines',
        autoDetectNumbers: 'Auto-detect Numbers',
        autoDetectBooleans: 'Auto-detect Booleans',
      },
      delimiters: {
        comma: 'Comma (,)',
        semicolon: 'Semicolon (;)',
        tab: 'Tab (\\t)',
        pipe: 'Pipe (|)',
        space: 'Space',
      },
      formats: {
        jsonObject: 'JSON Objects',
        jsonArray: 'JSON Array',
      },
      preview: {
        title: 'Data Preview',
        firstRows: 'first {count} rows',
        rowsDetected: '{count} rows detected',
      },
      convert: 'Convert to JSON',
      output: {
        title: 'JSON Output',
        complete: 'Conversion Complete',
        recordsConverted: '{count} records converted',
        noOutput: 'No JSON output yet. Please input CSV data to convert.',
      },
    },
    excelTextToJson: {
      title: 'Excel Text to JSON',
      description: 'Convert Excel clipboard data directly to JSON format',
      introduction: {
        title: 'Tool Introduction',
        description:
          'Online Excel text to JSON converter for converting tab-separated Excel data to JSON format.',
        usage:
          'Copy data from Excel and paste it here. Default delimiter is tab (\\t). First row should contain headers for object format.',
      },
      example: {
        title: 'Example',
        input: 'Excel Input:',
        output: 'JSON Output:',
      },
      input: {
        title: 'Input Excel Data',
        placeholder:
          'Paste your Excel data here...\n\nExample:\nname\tage\tscore\nLi Hua\t25\t89\nXiao Ming\t22\t85',
        fileUpload: 'Upload Text File',
      },
      options: {
        title: 'Parsing Options',
        delimiter: 'Delimiter',
        outputFormat: 'Output Format',
        hasHeaders: 'First Row as Headers',
        skipEmptyLines: 'Skip Empty Lines',
        autoDetectNumbers: 'Auto-detect Numbers',
        autoDetectBooleans: 'Auto-detect Booleans',
      },
      delimiters: {
        comma: 'Comma (,)',
        semicolon: 'Semicolon (;)',
        tab: 'Tab (\\t)',
        pipe: 'Pipe (|)',
        space: 'Space',
      },
      formats: {
        jsonObject: 'JSON Objects',
        jsonArray: 'JSON Array',
      },
      preview: {
        title: 'Data Preview',
        firstRows: 'first {count} rows',
        rowsDetected: '{count} rows detected',
      },
      convert: 'Convert to JSON',
      output: {
        title: 'JSON Output',
        complete: 'Conversion Complete',
        recordsConverted: '{count} records converted',
        noOutput: 'No JSON output yet. Please input Excel data to convert.',
      },
    },
    jsonPathExtractor: {
      title: 'JSON Path Extractor',
      description:
        'Extract data from JSON using JSONPath expressions with advanced filtering capabilities',
      extractButton: 'Extract Data',
      features: {
        pathExtraction: {
          title: 'Path Extraction',
          description:
            'Use JSONPath expressions to precisely extract data from complex JSON structures with dot notation and array indexing.',
        },
        filtering: {
          title: 'Advanced Filtering',
          description:
            'Support for wildcards, array slicing, and conditional filtering to extract exactly the data you need.',
        },
        export: {
          title: 'Export Results',
          description:
            'Copy extracted data to clipboard or download as JSON file with formatted output and statistics.',
        },
      },
      syntaxGuide: {
        title: 'JSONPath Syntax Guide',
        basicSyntax: 'Basic Syntax',
        examples: 'Common Examples',
        rootSymbol: 'Root of JSON data',
        dotNotation: 'Access object properties',
        bracketNotation: 'Array/object access',
        wildcard: 'Match all elements',
        exampleDesc1: 'Get first book title',
        exampleDesc2: 'Get all book authors',
      },
      inputSection: {
        title: 'JSON Data & Path',
        jsonData: 'JSON Data',
        jsonPath: 'JSONPath Expression',
        jsonPlaceholder: 'Paste your JSON data here...',
        pathPlaceholder: 'Enter JSONPath expression (e.g., $.users[*].name)',
        quickPaths: 'Quick Path Templates',
      },
      outputSection: {
        title: 'Extracted Results',
        noResults: 'No extraction results yet. Please input JSON data and JSONPath expression.',
        extractedData: 'Extracted Data',
      },
      quickPaths: {
        root: 'Root element',
        allProperties: 'All properties',
        firstArrayItem: 'First array item',
        allArrayItems: 'All array items',
        lastArrayItem: 'Last array item',
        arraySlice: 'Array slice (0-2)',
      },
      success: {
        validJson: 'Valid JSON format',
        extracted: 'Data Extracted Successfully',
        arrayResults: 'Found {count} array items',
        objectResults: 'Found object with {count} properties',
        primitiveResult: 'Found {type} value',
      },
      errors: {
        invalidJson: 'Invalid JSON format',
        pathError: 'JSONPath Expression Error',
        noMatches: 'No data matches the specified path',
      },
      messages: {
        copied: 'Extracted data copied to clipboard successfully!',
        copyFailed: 'Failed to copy to clipboard',
        downloaded: 'JSON file downloaded successfully!',
        downloadFailed: 'Failed to download file',
      },
    },
    jsonFormatter: {
      title: 'JSON Formatter',
      description: 'Format, beautify, and validate JSON data with customizable indentation',
      inputTitle: 'Input JSON',
      outputTitle: 'Formatted JSON',
      inputPlaceholder: 'Paste your JSON here...',
      noResults: 'No formatted JSON yet. Please input valid JSON to format.',
      validJson: 'Valid JSON',
      invalidJson: 'Invalid JSON',
      formattingComplete: 'Formatting Complete',
      formatOptions: 'Format Options',
      indent: 'Indent',
      sortKeys: 'Sort Keys',
      compactFormat: 'Compact Format',
      escapeUnicode: 'Escape Unicode',
      formatJson: 'Format JSON',
      spaces2: '2 spaces',
      spaces4: '4 spaces',
      tab: 'Tab',
      lines: 'lines',
      characters: 'characters',
      smaller: 'smaller',
      larger: 'larger',
      caseOptions: 'Case Options',
      keyCase: 'Key Case',
      valueCase: 'Value Case',
      preserveCase: 'Preserve Original Case',
      toUpperCase: 'Convert to Uppercase',
      toLowerCase: 'Convert to Lowercase',
      features: {
        prettyFormat: {
          title: 'Pretty Format',
          description: 'Automatically format and beautify JSON with proper indentation and spacing',
        },
        validation: {
          title: 'Validation',
          description: 'Real-time JSON validation with detailed error messages and line numbers',
        },
        customization: {
          title: 'Customization',
          description: 'Choose indentation size, sorting, and compact formatting options',
        },
      },
      messages: {
        formatSuccess: 'JSON formatted successfully!',
        formatError: 'Failed to format JSON: ',
        provideData: 'Please provide JSON data to format',
        copied: 'JSON copied to clipboard successfully!',
        copyFailed: 'Failed to copy to clipboard',
        downloaded: 'JSON file downloaded successfully!',
        downloadFailed: 'Failed to download file',
      },
    },
    jsonMerge: {
      title: 'JSON File Merger',
      description: 'Merge multiple JSON files into a single file',
      introduction: {
        title: 'Tool Introduction',
        description:
          'Online JSON file merger tool to combine multiple JSON files into one large JSON file.',
        usage:
          'JSON files will be merged in import order. If order matters, please note the file sequence.',
      },
      fileUpload: {
        title: 'Upload JSON Files',
        description:
          'Select multiple JSON files to merge. Files will be processed in the order shown below.',
        selectFiles: 'Select JSON Files',
        supportedFormats: 'Supports .json files',
        noFiles: 'No files selected yet. Please select JSON files to merge.',
      },
      filePreview: {
        title: 'File Preview',
        fileName: 'File Name',
        fileSize: 'File Size',
        jsonStructure: 'JSON Structure',
        arrayItems: '{count} array items',
        object: 'JSON Object',
        remove: 'Remove',
        moveUp: 'Move Up',
        moveDown: 'Move Down',
      },
      options: {
        title: 'Merge Options',
        outputFileName: 'Output File Name',
        outputFileNamePlaceholder: 'Enter output file name (without extension)',
        defaultFileName: 'merged-json',
      },
      actions: {
        merge: 'Merge JSON Files',
        clear: 'Clear All Files',
        download: 'Download Merged JSON',
      },
      output: {
        title: 'Merged JSON Output',
        noOutput: 'No merged output yet. Please upload JSON files and click Merge.',
        complete: 'Merge Complete',
        itemsMerged: '{count} items merged',
        downloadReady: 'Merged JSON file is ready for download.',
      },
      features: {
        multipleFiles: {
          title: 'Multiple File Support',
          description: 'Upload and merge multiple JSON files with drag-and-drop support.',
        },
        orderControl: {
          title: 'Order Control',
          description: 'Reorder files before merging to control the output sequence.',
        },
        preview: {
          title: 'File Preview',
          description: 'Preview file structure and content before merging.',
        },
      },
      errors: {
        noFiles: 'Please select at least one JSON file to merge',
        invalidJson: 'Invalid JSON in file: {fileName}',
        mergeFailed: 'Failed to merge JSON files: {error}',
        emptyArray: 'JSON file must contain an array at the root level',
      },
      success: {
        filesAdded: '{count} file(s) added successfully',
        mergeComplete: 'JSON files merged successfully!',
      },
    },
    jsonToExcel: {
      title: 'JSON to Excel/CSV/SQL Converter',
      description: 'Convert JSON data to Excel, CSV, or SQL format with customizable options',
      inputTitle: 'Input JSON Data',
      outputTitle: 'Excel Output',
      csvOutputTitle: 'CSV Output',
      sqlOutputTitle: 'SQL Output',
      inputPlaceholder: 'Paste your JSON array here...',
      noResults: 'No conversion results yet. Please input JSON data to convert.',
      conversionComplete: 'Conversion completed successfully!',
      readyForDownload: 'Excel file is ready for download.',
      csvReadyForDownload: 'CSV file is ready for download.',
      sqlReadyForDownload: 'SQL file is ready for download.',
      previewTitle: 'Data Preview',
      convertToExcel: 'Convert to Excel',
      convertToCsv: 'Convert to CSV',
      convertToSql: 'Generate SQL',
      showingRows: 'Showing {shown} of {total} rows',
      options: {
        conversionType: 'Conversion Type',
        includeHeaders: 'Include Headers',
        autoFitColumns: 'Auto-fit Columns',
        sheetName: 'Sheet Name',
        sheetNamePlaceholder: 'Enter sheet name',
        delimiter: 'Delimiter',
        quoteChar: 'Quote Character',
        flattenNested: 'Flatten Nested Objects',
        comma: 'Comma',
        semicolon: 'Semicolon',
        tab: 'Tab',
        pipe: 'Pipe',
        doubleQuote: 'Double Quote',
        singleQuote: 'Single Quote',
        none: 'None',
        sqlOptions: 'SQL Options',
        tableName: 'Table Name',
        tableNamePlaceholder: 'Enter table name',
        sqlType: 'SQL Type',
        whereField: 'WHERE Field',
        whereFieldPlaceholder: 'Field for WHERE clause',
        escapeValues: 'Escape Values',
        batchInsert: 'Batch Insert',
      },
      features: {
        conversion: {
          title: 'Smart Conversion',
          description:
            'Automatically convert JSON arrays to Excel, CSV, or SQL format with proper data type handling.',
        },
        formatting: {
          title: 'Excel Formatting',
          description:
            'Generate properly formatted Excel files with headers, auto-sized columns, and multiple sheets.',
        },
        batch: {
          title: 'Batch Processing',
          description:
            'Handle large datasets efficiently with preview and bulk download capabilities.',
        },
      },
      errors: {
        emptyInput: 'Please provide JSON data to convert',
        invalidJson: 'Invalid JSON format. Please check your input.',
        notArray: 'Input must be a JSON array',
        emptyArray: 'JSON array cannot be empty',
        conversionFailed: 'Failed to convert JSON',
        emptyTableName: 'Please provide a table name',
        emptyWhereField: 'Please provide a WHERE field for UPDATE statements',
      },
      success: {
        conversionComplete: 'JSON converted to Excel successfully!',
        csvConversionComplete: 'JSON converted to CSV successfully!',
        sqlConversionComplete: 'SQL statements generated successfully!',
      },
    },
    excelToJson: {
      title: 'Excel to JSON Converter',
      description: 'Convert Excel files to JSON format with flexible parsing options',
      inputTitle: 'Upload Excel File',
      outputTitle: 'JSON Output',
      uploadDescription: 'Select an Excel file to convert to JSON',
      selectFile: 'Select Excel File',
      supportedFormats: 'Supports .xlsx, .xls, .csv, and .ods files',
      noResults: 'No conversion results yet. Please upload an Excel file.',
      conversionComplete: 'Conversion completed successfully!',
      recordsCount: '{count} records converted',
      convert: 'Convert to JSON',
      fileSelected: 'File selected successfully',
      options: {
        title: 'Conversion Options',
        firstRowAsHeaders: 'First Row as Headers',
        skipEmptyRows: 'Skip Empty Rows',
        sheetIndex: 'Sheet to Convert',
      },
      features: {
        parsing: {
          title: 'Excel Parsing',
          description:
            'Parse Excel files with support for multiple sheets, formulas, and data types.',
        },
        conversion: {
          title: 'Flexible Conversion',
          description:
            'Convert with options for headers, empty rows, and specific sheet selection.',
        },
        options: {
          title: 'Conversion Options',
          description:
            'Customize output with header handling, empty row skipping, and sheet selection.',
        },
      },
      errors: {
        noFileSelected: 'Please select an Excel file to convert',
        xlsxRequired:
          'XLSX library is required for Excel file parsing. Please install: npm install xlsx',
        conversionFailed: 'Failed to convert Excel to JSON',
      },
    },
    jsonToSql: {
      title: 'JSON to SQL Converter',
      description: 'Generate SQL INSERT, UPDATE, or CREATE TABLE statements from JSON data',
      inputTitle: 'Input JSON Data',
      outputTitle: 'SQL Output',
      inputPlaceholder: 'Paste your JSON array here...',
      noResults: 'No SQL statements generated yet. Please input JSON data and configure options.',
      conversionComplete: 'SQL generation completed successfully!',
      statementsGenerated: '{count} SQL statements generated',
      convert: 'Generate SQL',
      options: {
        title: 'SQL Options',
        tableName: 'Table Name',
        tableNamePlaceholder: 'Enter table name',
        sqlType: 'SQL Type',
        whereField: 'WHERE Field',
        whereFieldPlaceholder: 'Field for WHERE clause',
        escapeValues: 'Escape Values',
        batchInsert: 'Batch Insert',
      },
      features: {
        insertion: {
          title: 'Multiple SQL Types',
          description: 'Generate INSERT, UPDATE, or CREATE TABLE statements from JSON data.',
        },
        customization: {
          title: 'Customization',
          description: 'Configure table names, SQL types, and field mappings for your database.',
        },
        security: {
          title: 'SQL Security',
          description: 'Automatic value escaping to prevent SQL injection vulnerabilities.',
        },
      },
      errors: {
        emptyInput: 'Please provide JSON data to convert',
        emptyTableName: 'Please provide a table name',
        emptyWhereField: 'Please provide a WHERE field for UPDATE statements',
        invalidJson: 'Invalid JSON format. Please check your input.',
        notArray: 'Input must be a JSON array',
        emptyArray: 'JSON array cannot be empty',
        conversionFailed: 'Failed to generate SQL statements',
      },
      success: {
        conversionComplete: 'SQL statements generated successfully!',
      },
    },
    fileRenamer: {
      title: 'File Renamer Tool',
      description: 'Batch rename files with multiple modes - local processing for privacy',
      uploadArea: {
        title: 'Drag & Drop Files Here',
        subtitle: 'or click to select files',
        selectFiles: 'Select Files',
      },
      fileCount: 'Total files: {count}',
      totalSize: 'Total size: {size}',
      tabs: {
        sequential: 'Sequential',
        replace: 'Find & Replace',
        case: 'Case Transform',
        insert: 'Insert Text',
        truncate: 'Truncate',
        script: 'Generate Script',
      },
      sequential: {
        prefix: 'Prefix',
        prefixPlaceholder: 'e.g., photo_',
        startNumber: 'Start Number',
        padding: 'Number Padding',
      },
      replace: {
        findText: 'Find Text',
        findPlaceholder: 'Text to find',
        replaceText: 'Replace With',
        replacePlaceholder: 'Replacement text',
        caseSensitive: 'Case Sensitive',
      },
      case: {
        transformation: 'Case Transformation',
        uppercase: 'UPPERCASE',
        lowercase: 'lowercase',
        capitalize: 'Capitalize Words',
      },
      insert: {
        text: 'Text to Insert',
        textPlaceholder: 'Text to insert',
        position: 'Position',
        prefix: 'At Beginning',
        suffix: 'At End',
        atIndex: 'At Index',
        index: 'Insert Index',
      },
      truncate: {
        startIndex: 'Start Index',
        endIndex: 'End Index',
        description: 'Extract substring from start index to end index (0-based)',
      },
      script: {
        scriptType: 'Script Type',
        windows: 'Windows Batch (.bat)',
        linux: 'Linux Shell (.sh)',
        autoGenerated: 'Auto-generated script',
        scriptPreview: 'Script Preview',
        downloadScript: 'Download Script',
        copyScript: 'Copy Script',
        noContent:
          'No script content available. Add files and apply renaming options to generate script.',
        instructions: {
          title: 'Instructions',
          description:
            'This tool generates scripts to rename your files. The script is automatically generated based on your files and renaming options. Click "Download Script" to download it. Place the script in the directory with your files and run it to perform the renaming operation.',
        },
      },
      actions: {
        preview: 'Preview',
        apply: 'Apply Rename',
        download: 'Download ZIP',
        clear: 'Clear Files',
      },
      sorting: {
        title: 'Sorting',
        natural: 'Natural Sort',
        filename: 'Filename Order',
        modifiedTime: 'Modified Time',
        modifiedTimeDesc: 'Modified Time (Descending)',
        random: 'Random',
        reverse: 'Reverse Current Order',
        manual: 'Manual Sort (Drag & Drop)',
      },
      fileList: {
        title: 'File List',
        drag: 'Drag',
        originalName: 'Original Name',
        newName: 'New Name',
        size: 'Size',
        type: 'Type',
        dragHint: 'Drag files to reorder them manually',
      },
      messages: {
        filesAdded: '{count} file(s) added successfully!',
        renameApplied: 'Rename applied successfully!',
        downloadStarted: 'Download started! Please check your downloads folder.',
        downloadError: 'Download failed! Please try again.',
        filesCleared: 'All files cleared!',
        noFilesToProcess: 'No files to process! Please add files first.',
        noScriptToDownload: 'No script to download! Please generate a script first.',
        noScriptToCopy: 'No script to copy! Please generate a script first.',
        scriptDownloaded: 'Script "{fileName}" downloaded successfully!',
        scriptCopied: 'Script copied to clipboard successfully!',
        scriptCopyFailed: 'Failed to copy script to clipboard!',
      },
    },
    imageCompressor: {
      title: 'Image Compressor Master',
      description:
        'Efficient online image compression tool with batch processing and local privacy',
      settings: 'Compression Settings',
      quality: 'Quality',
      smaller: 'Smaller',
      larger: 'Larger',
      outputFormat: 'Output Format',
      keepOriginal: 'Keep Original Format',
      maxWidth: 'Max Width',
      uploadTitle: 'Drag & Drop Images or Click to Select',
      uploadDescription: 'Support multiple images, local processing, no upload to server',
      supportedFormats: 'Supported formats',
      selectFiles: 'Select Files',
      imageList: 'Image List',
      compressing: 'Compressing...',
      compressAll: 'Compress All',
      downloadAll: 'Download All',
      compress: 'Compress',
      remove: 'Remove',
      originalSize: 'Original Size',
      compressedSize: 'Compressed Size',
      spaceSaved: 'Space Saved',
      original: 'Original',
      compressed: 'Compressed',
      imagePreview: 'Image Preview',
      originalImage: 'Original Image',
      compressedImage: 'Compressed Image',
      size: 'Size',
      dimensions: 'Dimensions',
      saved: 'Saved',
      status: {
        pending: 'Pending',
        compressing: 'Processing',
        completed: 'Completed',
        error: 'Failed',
      },
      features: {
        efficient: {
          title: 'High Efficiency',
          description:
            'Advanced compression algorithms maintain image quality while reducing file size by 40-80%.',
        },
        secure: {
          title: 'Privacy Protection',
          description:
            'All processing happens locally in your browser. Images never uploaded to any server.',
        },
        batch: {
          title: 'Batch Processing',
          description:
            'Process multiple images simultaneously with progress tracking and batch download.',
        },
      },
      errors: {
        noValidImages: 'No valid image files found',
        compressionFailed: 'Failed to compress {filename}',
      },
      success: {
        compressionComplete: 'All images compressed successfully!',
        downloadComplete: 'Batch download completed!',
        pasteSuccess: 'Images pasted successfully!',
      },
    },
    imageToGifConverter: {
      title: 'Image to GIF Converter',
      description: 'Convert multiple images to animated GIFs with customizable timing and settings',
      howToUse: {
        title: 'How to Use',
        step1:
          'Upload multiple image files by clicking "Select Image Files" or dragging and dropping',
        step2: 'Adjust GIF settings (width, quality, frame rate)',
        step3: 'Set individual frame delays and reorder images as needed',
        step4: 'Click "Generate GIF" to create your animated GIF',
      },
      tips: {
        title: 'Tips for Best Results',
        tip1: 'For best results, use images with similar dimensions',
        tip2: 'Lower frame rates (1-5 FPS) create smoother animations',
        tip3: 'Smaller GIF widths (200-400px) load faster and consume less memory',
        tip4: 'Use medium quality for a good balance between file size and image quality',
      },
      upload: {
        title: 'Upload Images',
        dragDrop: 'Drag & drop your images here',
        selectFile: 'Select Image Files',
        supportedFormats: 'Supports JPG, PNG, WebP and other image formats',
      },
      settings: {
        width: 'GIF Width (px)',
        quality: 'Quality',
        fps: 'Frame Rate (FPS)',
        loopCount: 'Loop Count',
        infinite: 'Infinite',
        qualityOptions: {
          high: 'High Quality',
          medium: 'Medium Quality',
          low: 'Low Quality (Smaller File)',
        },
      },
      preview: {
        title: 'Image Preview & Controls',
        selectedImages: 'Selected Images',
        moveUp: 'Move First to End',
        moveDown: 'Move Last to Start',
        reverse: 'Reverse Order',
        shuffle: 'Shuffle Images',
      },
      actions: {
        generateGif: 'Generate GIF',
      },
      processing: {
        title: 'Processing Images',
        description: 'Converting your images to GIF. This may take a moment...',
        preview: 'Preview',
      },
      result: {
        title: 'Generated GIF',
        download: 'Download GIF',
        createNew: 'Create New GIF',
      },
      features: {
        conversion: {
          title: 'Image Conversion',
          description:
            'Convert multiple images to high-quality animated GIFs with customizable frame rate and dimensions.',
        },
        customization: {
          title: 'Full Customization',
          description:
            'Control every aspect including quality, size, timing, and loop count for perfect results.',
        },
        animation: {
          title: 'Animation Control',
          description:
            'Set individual frame delays, reorder images, and control animation loop behavior.',
        },
      },
      errors: {
        noImages: 'Please select valid image files.',
        processingFailed: 'Failed to process images. Please try again.',
        noImagesSelected: 'Please select image files first.',
        fileProcessing: 'Failed to process selected files.',
      },
      messages: {
        filesAdded: '{count} image(s) added successfully!',
        gifGenerated: 'GIF generated successfully!',
        filesPasted: '{count} image(s) pasted from clipboard!',
        cleared: 'All images cleared!',
      },
    },
    gifEditor: {
      title: 'GIF Editor',
      description: 'Split, edit and modify GIF frames with custom timing and settings',
      howToUse: {
        title: 'How to Use',
        step1: 'Upload a GIF file by clicking "Select GIF File" or dragging and dropping',
        step2: 'Adjust GIF settings (width, quality, frame rate)',
        step3: 'Modify individual frame delays, reorder frames or delete unwanted frames',
        step4: 'Click "Generate GIF" to create your edited GIF',
      },
      tips: {
        title: 'Tips for Best Results',
        tip1: 'For best results, use GIFs with consistent frame dimensions',
        tip2: 'Frame delays below 20ms may not display correctly in some browsers',
        tip3: 'Smaller GIF widths (200-400px) load faster and consume less memory',
        tip4: 'Medium quality provides a good balance between file size and image quality',
      },
      upload: {
        title: 'Upload GIF',
        dragDrop: 'Drag GIF here',
        selectFile: 'Select GIF File',
        supportedFormats: 'GIF format only (max: 50MB)',
      },
      settings: {
        width: 'GIF Width (pixels)',
        quality: 'Quality',
        fps: 'Frame Rate (FPS)',
        preserveOriginal: 'Preserved from original GIF',
        qualityOptions: {
          high: 'High Quality',
          medium: 'Medium Quality',
          low: 'Low Quality (smaller file)',
        },
      },
      preview: {
        title: 'GIF Preview and Controls',
        originalGif: 'Original GIF',
        frames: 'Frames',
        frame: 'Frame',
        delay: 'Delay',
        dimensions: 'Dimensions',
        pixels: 'pixels',
        moveUp: 'Move First to End',
        moveDown: 'Move Last to Beginning',
        reverse: 'Reverse Order',
        shuffle: 'Shuffle Frames',
      },
      actions: {
        generateGif: 'Generate GIF',
      },
      processing: {
        title: 'Processing GIF',
        description: 'Editing your GIF frames. This may take some time...',
        preview: 'Preview',
      },
      result: {
        title: 'Generated GIF',
        download: 'Download GIF',
        createNew: 'Edit Another GIF',
      },
      features: {
        frameEditing: {
          title: 'Frame Editing',
          description:
            'Split GIF into individual frames and modify delay, order or delete unwanted frames.',
        },
        customization: {
          title: 'Full Customization',
          description:
            'Control quality, size, timing, frame order and all aspects for perfect results.',
        },
        animation: {
          title: 'Animation Control',
          description:
            'Set individual frame delays, reorder frames and control animation loop behavior.',
        },
      },
      errors: {
        noGif: 'Please select a valid GIF file.',
        invalidFile: 'Please select a valid GIF file.',
        fileTooLarge: 'File size must be less than 50MB.',
        processingFailed: 'Failed to process GIF, please try again.',
        noFrames: 'No frames to process. Please upload a GIF file first.',
        fileProcessing: 'Failed to process selected file.',
        frameParsingFailed: 'Failed to parse GIF frames.',
      },
      messages: {
        fileLoaded: 'GIF file loaded successfully!',
        gifGenerated: 'GIF generated successfully!',
        filePasted: 'Pasted GIF file from clipboard!',
      },
    },
    svgEditor: {
      title: 'SVG Editor',
      description: 'Edit SVG code with real-time preview and visual editor',
      howToUse: {
        title: 'How to Use',
        step1: 'Edit SVG code directly in the editor or use the visual editor',
        step2: 'See real-time preview of your SVG',
        step3: 'Load tutorials to learn SVG basics',
        step4: 'Download your SVG when finished',
      },
      tips: {
        title: 'Tips for Best Results',
        tip1: 'Use the visual editor to quickly create basic shapes',
        tip2: 'Load tutorials to learn advanced SVG techniques',
        tip3: 'Copy SVG code to clipboard for use in other applications',
        tip4: 'Download SVG files for use in web projects',
      },
      editor: {
        title: 'SVG Code Editor',
        loadExample: 'Load Example',
        clear: 'Clear',
        placeholder: 'Enter your SVG code here...',
        lines: 'Lines',
        copy: 'Copy to Clipboard',
      },
      preview: {
        title: 'SVG Preview',
        empty: 'No SVG code to display. Add some SVG code to see the preview.',
        dimensions: 'Dimensions',
        download: 'Download SVG',
      },
      visualEditor: {
        title: 'Visual Editor',
        shapes: 'Shapes',
        history: 'History',
        tools: 'Tools',
        delete: 'Delete Selected',
        clear: 'Clear Canvas',
        undo: 'Undo',
        redo: 'Redo',
        properties: 'Properties',
        fill: 'Fill Color',
        stroke: 'Stroke Color',
        strokeWidth: 'Stroke Width',
        rotation: 'Rotation',
        transparent: 'Transparent',
        noSelection: 'No shape selected',
      },
      shapes: {
        rectangle: 'Rectangle',
        circle: 'Circle',
        ellipse: 'Ellipse',
        line: 'Line',
        triangle: 'Triangle',
        path: 'Path',
        polygon: 'Polygon',
        star: 'Star',
        heart: 'Heart',
        quadraticCurve: 'Quadratic Curve',
        cubicCurve: 'Cubic Curve',
        arcCurve: 'Arc Curve',
      },
      tutorials: {
        title: 'SVG Tutorials',
        viewTutorial: 'View Tutorial',
        basicShapes: 'Basic Shapes',
        basicShapesDesc: 'Learn to create rectangles, circles, and ellipses',
        paths: 'Paths and Polylines',
        pathsDesc: 'Draw custom shapes using paths',
        gradients: 'Gradients and Patterns',
        gradientsDesc: 'Add gradients and patterns to your SVGs',
      },
      errors: {
        copyFailed: 'Failed to copy code to clipboard',
        noSvg: 'No SVG code to download',
      },
      messages: {
        exampleLoaded: 'Example SVG loaded successfully!',
        editorCleared: 'Editor cleared!',
        codeCopied: 'SVG code copied to clipboard!',
        svgDownloaded: 'SVG downloaded successfully!',
        tutorialLoaded: 'Tutorial loaded!',
        shapeDeleted: 'Selected shape deleted!',
        canvasCleared: 'Canvas cleared!',
      },
    },
    imageWatermark: {
      title: 'Image Watermark Master',
      description:
        'Add text or image watermarks to your photos with customizable styles and positions',
      settings: 'Watermark Settings',
      watermarkType: 'Watermark Type',
      textWatermark: 'Text Watermark',
      textWatermarkDesc: 'Add text watermark to images',
      imageWatermark: 'Image Watermark',
      imageWatermarkDesc: 'Add image watermark to images',
      combinedWatermark: 'Combined Watermark',
      combinedWatermarkDesc: 'Add both text and image watermarks',
      textSettings: 'Text Settings',
      watermarkText: 'Watermark Text',
      textPlaceholder: 'Enter watermark text',
      fontSize: 'Font Size',
      fontColor: 'Font Color',
      fontFamily: 'Font Family',
      imageSettings: 'Image Settings',
      watermarkImage: 'Watermark Image',
      uploadWatermark: 'Upload Watermark Image',
      watermarkPreview: 'Watermark Preview',
      removeWatermark: 'Remove Watermark',
      imageWidth: 'Image Width',
      imageOpacity: 'Image Opacity',
      positionSettings: 'Position Settings',
      position: 'Position',
      margin: 'Margin',
      advancedSettings: 'Advanced Settings',
      opacity: 'Opacity',
      rotation: 'Rotation',
      scale: 'Scale',
      topLeft: 'Top Left',
      topCenter: 'Top Center',
      topRight: 'Top Right',
      centerLeft: 'Center Left',
      center: 'Center',
      centerRight: 'Center Right',
      bottomLeft: 'Bottom Left',
      bottomCenter: 'Bottom Center',
      bottomRight: 'Bottom Right',
      uploadTitle: 'Drag & Drop Images or Click to Select',
      uploadDescription: 'Support multiple images, local processing, no upload to server',
      supportedFormats: 'Supported formats',
      selectFiles: 'Select Files',
      imageList: 'Image List',
      processing: 'Processing...',
      processAll: 'Process All',
      downloadAll: 'Download All',
      process: 'Process',
      remove: 'Remove',
      originalSize: 'Original Size',
      processedSize: 'Processed Size',
      processed: 'Processed',
      original: 'Original',
      imagePreview: 'Image Preview',
      originalImage: 'Original Image',
      processedImage: 'Processed Image',
      size: 'Size',
      dimensions: 'Dimensions',
      gifWarning: 'Watermark will be applied to all frames of the animated GIF',
      status: {
        pending: 'Pending',
        processing: 'Processing',
        completed: 'Completed',
        error: 'Failed',
      },
      features: {
        watermark: {
          title: 'Multiple Watermark Types',
          description:
            'Add text watermarks, image watermarks, or combine both with full customization options.',
        },
        batch: {
          title: 'Batch Processing',
          description:
            'Process multiple images simultaneously with progress tracking and batch download.',
        },
        customization: {
          title: 'Full Customization',
          description:
            'Adjust watermark position, opacity, rotation, scale, font properties, and more.',
        },
      },
      errors: {
        noValidImages: 'No valid image files found',
        invalidWatermark: 'Please select a valid image file for watermark',
        noWatermarkImage: 'Please upload a watermark image',
        noWatermarkText: 'Please enter watermark text',
        watermarkProcessingFailed: 'Failed to process watermark image',
        processingFailed: 'Failed to process {filename}',
      },
      success: {
        processingComplete: 'All images processed successfully!',
        downloadComplete: 'Batch download completed!',
        pasteSuccess: 'Images pasted successfully!',
      },
    },
    faviconGenerator: {
      title: 'Favicon Generator',
      description: 'Generate professional favicons in multiple sizes and formats from any image',
      uploadSection: 'Upload Image',
      uploadTitle: 'Drag & Drop Image or Click to Select',
      uploadDescription: 'Upload any image to create favicons for your website',
      supportedFormats: 'Supported formats',
      selectImage: 'Select Image',
      cropImage: 'Crop Image',
      originalImage: 'Original Image',
      originalImageDescription: 'Full resolution image preview - this is your source image',
      imageSize: 'Image Size',
      cropPreview: 'Crop Preview',
      selectAnother: 'Select Another',
      cropInstruction:
        'Drag the crop area to move it, or drag the corner handles to resize. The selected square area will be used to generate favicons.',
      cropInstructionAdvanced:
        'Drag to move the crop area, resize by dragging corners, or use mouse wheel to zoom. The selected square area will be used for favicon generation.',
      outputFormat: 'Output Format',
      sizes: 'Favicon Sizes',
      generate: 'Generate Favicons',
      generating: 'Generating...',
      generatedFavicons: 'Generated Favicons',
      downloadAll: 'Download All as ZIP',
      usageInstructions: 'How to Use Favicons',
      htmlUsage: 'HTML Implementation',
      tips: 'Best Practices',
      tip1: 'Use simple, recognizable designs that work at small sizes',
      tip2: 'Ensure good contrast for visibility across different backgrounds',
      tip3: 'Test your favicon on various devices and browsers',
      tip4: 'Place favicon.ico in your website root directory for automatic detection',
      pasteHint: 'Tip: You can also paste images directly from your clipboard',
      features: {
        cropping: {
          title: 'Smart Cropping',
          description:
            'Interactive crop tool to select the perfect square area from your image with real-time preview.',
        },
        multiSize: {
          title: 'Multiple Sizes',
          description:
            'Generate favicons in all standard sizes (16px to 128px) for optimal compatibility across devices.',
        },
        formats: {
          title: 'Multiple Formats',
          description:
            'Export in ICO, PNG, or JPG formats to meet different browser and platform requirements.',
        },
      },
      errors: {
        invalidFile: 'Please select a valid image file',
        generationFailed: 'Failed to generate favicons',
        downloadFailed: 'Failed to download files',
        imageLoadFailed: 'Failed to load image',
        fileReadFailed: 'Failed to read file',
      },
      success: {
        imageLoaded: 'Image loaded successfully!',
        generationComplete: 'Favicons generated successfully!',
        downloadComplete: 'Download completed!',
      },
      messages: {
        pasteSuccess: 'Image pasted from clipboard and processing started!',
      },
    },
    cookieToJson: {
      title: 'Cookie to JSON',
      description: 'Convert cookie strings to JSON objects with parsing options',
      inputTitle: 'Input Cookie String',
      outputTitle: 'JSON Output',
      inputNote: 'Paste cookie string in the format:',
      inputPlaceholder:
        'Paste your cookie string here, e.g.:\nsessionId=abc123; userId=12345; theme=dark; lang=en-US\n\nSupported formats:\n- Standard cookie format: name1=value1; name2=value2\n- URL-encoded values are automatically decoded\n- Handles cookies without values (flags)',
      parseOptions: 'Parse Options',
      noResults: 'No conversion results yet. Please input a cookie string to convert.',
      error: 'Parse Error',
      success: 'Parsing Successful',
      conversionComplete: 'Conversion Complete',
      cookiesFound: '{count} cookies found',
      statistics: '{total} total cookies, {nonEmpty} with values',
      options: {
        decodeValues: 'Decode URL-encoded Values',
        removeEmpty: 'Remove Empty Values',
        formatOutput: 'Format JSON Output',
      },
      features: {
        parsing: {
          title: 'Cookie Parsing',
          description:
            'Automatically parse cookie strings with support for standard HTTP cookie format and URL decoding.',
        },
        conversion: {
          title: 'JSON Conversion',
          description:
            'Convert parsed cookies to clean JSON format with customizable output formatting options.',
        },
        export: {
          title: 'Export Options',
          description:
            'Copy to clipboard or download as JSON file with statistics and validation feedback.',
        },
      },
      errors: {
        noCookies: 'No valid cookies found in the input string',
        parseError: 'Failed to parse cookie string: {error}',
      },
      messages: {
        copied: 'JSON copied to clipboard successfully!',
        copyFailed: 'Failed to copy to clipboard',
        downloaded: 'JSON file downloaded successfully!',
        downloadFailed: 'Failed to download file',
      },
    },
    universalConverter: {
      title: 'Universal Format Converter',
      description: 'Convert between JSON, XML, and HTTP query parameters in real-time',
      inputTitle: 'Input',
      outputTitle: 'Output',
      format: 'Format',
      formatButton: 'Format',
      conversionDirection: 'Conversion Direction',
      conversionDirectionDescription: 'Choose which direction to convert or swap panels',
      swap: 'Swap Panels',
      convertLeft: 'Convert to Left',
      convertRight: 'Convert to Right',
      features: {
        bidirectional: {
          title: 'Bidirectional Conversion',
          description: 'Convert between any of the supported formats in both directions',
        },
        realtime: {
          title: 'Real-time Conversion',
          description: 'See conversions happen instantly as you type with automatic detection',
        },
        validation: {
          title: 'Format Validation',
          description: 'Built-in validation for all supported formats with detailed error messages',
        },
      },
      errors: {
        conversionFailed: 'Conversion failed. Please check your input format.',
        unsupportedFormat: 'Unsupported format selected',
        invalidJson: 'Invalid JSON format. Please check your input.',
        invalidXml: 'Invalid XML format. Please check your input.',
        invalidQuery: 'Invalid query parameter format. Please check your input.',
        xmlGenerationFailed: 'Failed to generate XML output',
        queryGenerationFailed: 'Failed to generate query parameter output',
        formatFailed: 'Format failed. Please check your input format.',
      },
    },
    qrCodeTool: {
      title: 'QR Code Generator & Recognizer',
      description:
        'Generate QR codes from text and recognize QR codes from images with batch processing support',
      tabs: {
        generate: 'Generate',
        recognize: 'Recognize',
      },
      generate: {
        inputTitle: 'Generate QR Code',
        textInputLabel: 'Text to encode',
        textInputPlaceholder:
          'Enter text to generate QR code...\nFor batch mode, enter one text per line',
        modeLabel: 'Generation Mode',
        singleMode: 'Single QR Code',
        batchMode: 'Batch QR Codes',
        singleModeHint: 'Generates one QR code from all the text',
        batchModeHint: 'Generates multiple QR codes, one for each line of text',
        generateSingleButton: 'Generate QR Code',
        generateBatchButton: 'Generate Batch QR Codes',
        singleGeneratedTitle: 'Generated QR Code',
        batchGeneratedTitle: 'Generated QR Codes ({count})',
        downloadAll: 'Download All as ZIP',
      },
      recognize: {
        uploadTitle: 'Recognize QR Codes',
        uploadInstruction: 'Upload QR Code Images',
        uploadDescription:
          'Drag and drop images here or click to select files. Supports JPG, PNG, WebP formats.',
        pasteHint: 'Tip: You can also paste images directly from your clipboard',
        selectFiles: 'Select Files',
        resultsTitle: 'Recognition Results',
        copyAll: 'Copy All Results',
        recognitionFailed: 'Failed to recognize QR code',
      },
      features: {
        batch: {
          title: 'Batch Processing',
          description:
            'Generate multiple QR codes at once or recognize QR codes from multiple images simultaneously.',
        },
        generate: {
          title: 'QR Generation',
          description:
            'Create high-quality QR codes from any text input with customizable options.',
        },
        recognize: {
          title: 'QR Recognition',
          description:
            'Extract data from QR codes in images with support for various image formats.',
        },
      },
      messages: {
        generateSuccess: 'QR code generated successfully!',
        batchGenerateSuccess: 'Generated {count} QR codes successfully!',
        downloadAllSuccess: 'All QR codes downloaded as ZIP file!',
        copySuccess: 'QR code copied to clipboard!',
        copyAllSuccess: 'All recognition results copied to clipboard!',
        recognitionComplete: 'QR code recognition completed!',
        pasteSuccess: 'Image pasted from clipboard and processing started!',
      },
      errors: {
        generateFailed: 'Failed to generate QR code. Please try again.',
        batchGenerateFailed: 'Failed to generate batch QR codes. Please check your input.',
        emptyBatch: 'Please enter at least one text to generate QR codes.',
        downloadAllFailed: 'Failed to download all QR codes. Please try again.',
        copyFailed: 'Failed to copy QR code to clipboard.',
        copyAllFailed: 'Failed to copy recognition results to clipboard.',
        noValidImages: 'Please select valid image files.',
        noQRCodeFound: 'No QR code found in the image.',
        noResultsToCopy: 'No successful recognition results to copy.',
      },
    },
    webRtcFileTransfer: {
      title: 'WebRTC File Transfer',
      description:
        'Transfer files directly between devices on the same network without intermediaries',
      deviceId: 'Device ID',
      status: {
        disconnected: 'Disconnected',
        connecting: 'Connecting',
        connected: 'Connected',
      },
      discovery: {
        title: 'Device Discovery',
        search: 'Search for Devices',
        searching: 'Searching...',
        foundDevices: 'Found Devices',
        noDevices: 'No devices found on the network',
        unknownDevice: 'Unknown Device',
        connect: 'Connect',
        demoDevice1: 'Demo Device 1',
        demoDevice2: 'Demo Device 2',
        connected: 'Connected to signaling server',
        disconnected: 'Not connected to signaling server',
      },
      connectionRequests: {
        title: 'Connection Requests',
        accept: 'Accept',
        reject: 'Reject',
      },
      transfer: {
        title: 'File Transfer',
        selectFile: 'Select File to Send',
        send: 'Send File',
        sending: 'Sending...',
        progress: 'Transfer Progress',
        receivedFiles: 'Received Files',
        download: 'Download',
        connectFirst: 'Please connect to a device first',
      },
      logs: {
        title: 'Activity Log',
        startDiscovery: 'Starting device discovery',
        devicesFound: 'Found {count} devices',
        connectingToDevice: 'Connecting to device {deviceId}',
        connectionEstablished: 'Connection established successfully',
        fileSelected: 'Selected file: {name} ({size})',
        sendingFile: 'Sending file: {name}',
        fileSent: 'File {name} sent successfully',
        receivingFile: 'Receiving file: {name} ({size})',
        fileReceived: 'Received file: {name}',
        unexpectedDataChunk: 'Received unexpected data chunk',
        initialized: 'WebRTC file transfer initialized',
        webRTCInitialized: 'WebRTC initialized successfully',
        webRTCInitFailed: 'Failed to initialize WebRTC: {error}',
        dataChannelOpened: 'Data channel opened',
        dataChannelClosed: 'Data channel closed',
        dataChannelError: 'Data channel error: {error}',
        messageReceived: 'Message received of type: {type}',
        iceCandidateGenerated: 'ICE candidate generated',
        iceCandidateSent: 'ICE candidate sent to signaling server',
        iceCandidateAdded: 'ICE candidate added',
        connectionStateChange: 'Connection state changed to: {state}',
        channelNotReady: 'Data channel is not ready for sending',
        sendFileFailed: 'Failed to send file: {error}',
        signalServerConnected: 'Connected to signaling server',
        signalServerDisconnected: 'Disconnected from signaling server',
        signalServerConnectionFailed: 'Failed to connect to signaling server: {error}',
        signalServerError: 'Signaling server error: {error}',
        connectingToSignalServer: 'Connecting to signaling server on port {port}',
        notConnectedToSignalServer: 'Not connected to signaling server',
        receivedDeviceId: 'Received device ID: {id}',
        deviceDiscovered: 'New device discovered: {id}',
        deviceDisconnected: 'Device disconnected: {id}',
        messageParseError: 'Failed to parse message from server: {error}',
        offerSent: 'Offer sent to target device',
        answerSent: 'Answer sent to source device',
        offerHandlingFailed: 'Failed to handle offer: {error}',
        answerHandlingFailed: 'Failed to handle answer: {error}',
        iceCandidateFailed: 'Failed to add ICE candidate: {error}',
        connectionRequestReceived: 'Connection request received from device {id}',
        unexpectedOffer: 'Unexpected offer received from device {id}',
        connectionTimeout: 'Connection timed out, resetting connection',
        connectionReset: 'Connection reset to initial state',
        acceptingConnection: 'Accepting connection from device {id}',
        connectionRequestRejected: 'Connection request from device {id} rejected',
      },
    },
    textSteganography: {
      title: 'Text Steganography',
      description: 'Hide secret messages within plain text using invisible Unicode characters',
      encryptionTitle: 'Encryption',
      decryptionTitle: 'Decryption',
      visibleText: 'Visible Text',
      visibleTextPlaceholder: 'Enter the text that will be visible',
      hiddenText: 'Hidden Text',
      hiddenTextPlaceholder: 'Enter the secret message to hide',
      steganographyText: 'Steganography Text',
      steganographyTextPlaceholder: 'Paste the steganography text here to decode',
      steganographyResult: 'Steganography Result',
      decodedText: 'Decoded Text',
      generateSteganography: 'Generate Steganography Text',
      features: {
        encryption: {
          title: 'Text Encryption',
          description: 'Hide secret messages within plain text using invisible Unicode characters',
        },
        decryption: {
          title: 'Text Decryption',
          description: 'Extract hidden messages from steganography text',
        },
        security: {
          title: 'Secure Communication',
          description: 'Share sensitive information discreetly through seemingly normal text',
        },
      },
      errors: {
        decodingFailed: 'Failed to decode the steganography text',
      },
    },
    imageSteganography: {
      title: 'Image Steganography',
      description:
        'Hide secret images within other images using LSB (Least Significant Bit) steganography technique',
      canvasTitle: 'Image Canvas',
      decodedImageTitle: 'Decoded Image',
      operationsTitle: 'Operations',
      decodingTitle: 'Decoding',
      canvasPlaceholder: 'Select an image to begin',
      decodedImagePlaceholder: 'Select an image to decode',
      exportImage: 'Export Image',
      exportDecodedImage: 'Export Decoded Image',
      modeToggle: {
        encode: 'Encode',
        decode: 'Decode',
      },
      step1: 'Step 1: Select Image to Hide',
      step1Desc: 'Choose the image you want to hide within another image',
      step2: 'Step 2: Save Hidden Image Data',
      step2Desc: 'Save the pixel data of the hidden image for steganography',
      step3: 'Step 3: Select Target Image',
      step3Desc: 'Choose the image in which you want to hide the secret image',
      step4: 'Step 4: Start Encryption',
      step4Desc: 'Embed the hidden image data into the target image',
      selectHiddenImage: 'Select Hidden Image',
      saveHiddenData: 'Save Hidden Image Data',
      selectTargetImage: 'Select Target Image',
      startEncryption: 'Start Encryption',
      decodeStep1: 'Step 1: Select Image to Decode',
      decodeStep1Desc: 'Choose the image containing hidden data to decode',
      decodeStep2: 'Step 2: Start Decoding',
      decodeStep2Desc: 'Extract the hidden image from the selected image',
      selectImageToDecode: 'Select Image to Decode',
      startDecoding: 'Start Decoding',
      decodedImagePreview: 'Decoded Image Preview',
      features: {
        encryption: {
          title: 'Image Encryption',
          description:
            'Hide secret images within other images using advanced steganography techniques',
        },
        decryption: {
          title: 'Image Decryption',
          description: 'Extract hidden images from steganography images',
        },
        steganography: {
          title: 'LSB Steganography',
          description: 'Utilize Least Significant Bit technique to embed data invisibly',
        },
        extraction: {
          title: 'Data Extraction',
          description: 'Recover hidden data from steganography images',
        },
        export: {
          title: 'Export Results',
          description: 'Download the steganography result as a PNG image file',
        },
        result: {
          title: 'Decoded Result',
          description: 'View and export the extracted hidden image',
        },
      },
      messages: {
        imageLoaded: 'Image loaded successfully',
        dataSaved: 'Hidden image data saved successfully',
        encryptionComplete: 'Encryption completed successfully',
        decryptionComplete: 'Decryption completed successfully',
        imageExported: 'Image exported successfully',
        canvasCleared: 'Canvas cleared',
      },
      errors: {
        imageLoadFailed: 'Failed to load image',
        dataSaveFailed: 'Failed to save hidden image data',
        encryptionFailed: 'Failed to encrypt image',
        decryptionFailed: 'Failed to decrypt image',
        exportFailed: 'Failed to export image',
      },
    },
    heartCollage: {
      title: 'Shape Collage Generator',
      description: 'Create beautiful collages in various shapes filled with your images',
      uploadTitle: 'Upload Images',
      uploadDescription: 'Drag and drop images here or click to select files',
      supportedFormats: 'Supported formats',
      selectFiles: 'Select Files',
      settings: 'Collage Settings',
      canvasSize: 'Canvas Size',
      shape: 'Shape',
      imageShape: 'Image Shape',
      arrangement: 'Arrangement',
      random: 'Random',
      grid: 'Grid',
      fitAll: 'Fit All Images',
      spacing: 'Spacing',
      additionalOptions: 'Additional Options',
      backgroundColor: 'Background Color',
      borderOptions: 'Border Options',
      showBorder: 'Show Border',
      small: 'Small',
      medium: 'Medium',
      large: 'Large',
      heart: 'Heart',
      square: 'Square',
      rectangle: 'Rectangle',
      circle: 'Circle',
      star: 'Star',
      rounded: 'Rounded',
      canvas: 'Collage Canvas',
      images: 'images',
      autoArrange: 'Auto Arrange',
      downloadCollage: 'Download Collage',
      selectedImages: 'Selected Images',
      dragInstructions: 'Drag images to reposition them within the shape. Drag corners to resize.',
      features: {
        collage: {
          title: 'Shape Collage',
          description: 'Create beautiful collages in various shapes filled with your images',
        },
        customization: {
          title: 'Customization Options',
          description: 'Customize canvas size, shapes, image shapes, spacing, and more',
        },
        export: {
          title: 'Export Results',
          description: 'Download your collage as a high-quality PNG image',
        },
      },
      messages: {
        filesAdded: '{count} file(s) added successfully',
        arranged: 'Images arranged within the shape',
        downloadSuccess: 'Collage downloaded successfully!',
        cleared: 'All images cleared',
      },
      errors: {
        noImages: 'Please select valid image files',
        fileProcessing: 'Failed to process selected files',
        noImagesSelected: 'Please select at least one image to arrange',
        downloadFailed: 'Failed to download collage',
      },
    },
  },
}
