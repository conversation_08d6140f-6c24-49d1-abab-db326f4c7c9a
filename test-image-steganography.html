<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Image Steganography Test</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      .container {
        display: flex;
        flex-direction: column;
        gap: 20px;
      }
      .canvas-container {
        border: 2px dashed #ccc;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        min-height: 300px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      canvas {
        max-width: 100%;
        max-height: 300px;
      }
      .controls {
        display: flex;
        flex-direction: column;
        gap: 10px;
      }
      button {
        padding: 10px 15px;
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
      }
      button:hover {
        background-color: #0056b3;
      }
      button:disabled {
        background-color: #ccc;
        cursor: not-allowed;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>Image Steganography Test</h1>

      <div class="canvas-container">
        <canvas id="steganography-canvas" width="500" height="500"></canvas>
        <div id="placeholder">Select an image to begin</div>
      </div>

      <div class="controls">
        <button id="selectHiddenImage">Select Hidden Image</button>
        <button id="saveHiddenData" disabled>Save Hidden Image Data</button>
        <button id="selectTargetImage">Select Target Image</button>
        <button id="startEncryption" disabled>Start Encryption</button>
        <button id="exportImage" disabled>Export Image</button>
        <button id="resetCanvas">Reset Canvas</button>
      </div>
    </div>

    <script>
      // Canvas setup
      const canvas = document.getElementById('steganography-canvas')
      const ctx = canvas.getContext('2d')
      const placeholder = document.getElementById('placeholder')

      // State
      let targetImageData = null
      let hiddenImageData = null
      let hiddenDataBinary = null

      // Buttons
      const selectHiddenImageBtn = document.getElementById('selectHiddenImage')
      const saveHiddenDataBtn = document.getElementById('saveHiddenData')
      const selectTargetImageBtn = document.getElementById('selectTargetImage')
      const startEncryptionBtn = document.getElementById('startEncryption')
      const exportImageBtn = document.getElementById('exportImage')
      const resetCanvasBtn = document.getElementById('resetCanvas')

      // Get file from input
      function getFile() {
        return new Promise((resolve) => {
          const input = document.createElement('input')
          input.type = 'file'
          input.accept = 'image/*'
          input.onchange = (e) => {
            const file = e.target.files?.[0] || null
            resolve(file)
          }
          input.click()
        })
      }

      // Select hidden image
      async function selectHiddenImage() {
        try {
          const file = await getFile()
          if (file) {
            const url = URL.createObjectURL(file)
            const img = new Image()
            img.onload = () => {
              // Draw image on canvas
              canvas.width = img.width
              canvas.height = img.height
              ctx.drawImage(img, 0, 0)

              // Get image data
              hiddenImageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
              placeholder.style.display = 'none'

              // Enable save button
              saveHiddenDataBtn.disabled = false

              URL.revokeObjectURL(url)
            }
            img.src = url
          }
        } catch (err) {
          console.error('Failed to load image:', err)
        }
      }

      // Save hidden image data
      function saveHiddenImageData() {
        if (!hiddenImageData) return

        try {
          // Process hidden image data to binary
          hiddenDataBinary = Array.from(hiddenImageData.data, (color) => {
            return color.toString(2).padStart(8, '0').split('')
          })

          alert('Hidden image data saved successfully!')
        } catch (err) {
          console.error('Failed to save data:', err)
        }
      }

      // Select target image
      async function selectTargetImage() {
        try {
          const file = await getFile()
          if (file) {
            const url = URL.createObjectURL(file)
            const img = new Image()
            img.onload = () => {
              // Draw image on canvas
              canvas.width = img.width
              canvas.height = img.height
              ctx.drawImage(img, 0, 0)

              // Get image data
              targetImageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
              placeholder.style.display = 'none'

              URL.revokeObjectURL(url)
            }
            img.src = url
          }
        } catch (err) {
          console.error('Failed to load image:', err)
        }
      }

      // Start encryption
      function drawHiddenData() {
        if (!hiddenDataBinary || !targetImageData) return

        try {
          // Put hidden data binary into one array
          const bigHiddenList = []
          for (let i = 0; i < hiddenDataBinary.length; i++) {
            bigHiddenList.push(...hiddenDataBinary[i])
          }

          // Process target image data
          const targetDataCopy = new Uint8ClampedArray(targetImageData.data)
          const targetDataBinary = Array.from(targetDataCopy, (color) => {
            // Make numbers even
            color = color > 254 ? color - 1 : color
            color = color % 2 == 1 ? color - 1 : color
            return color.toString(2).padStart(8, '0').split('')
          })

          // Embed hidden data into target data
          targetDataBinary.forEach((item, index) => {
            if (bigHiddenList[index]) {
              item[7] = bigHiddenList[index]
            }
          })

          // Convert binary back to pixel data
          const processedData = new Uint8ClampedArray(targetDataBinary.length)
          targetDataBinary.forEach((item, index) => {
            processedData[index] = parseInt(item.join(''), 2)
          })

          // Create new image data
          const newImageData = new ImageData(
            processedData,
            targetImageData.width,
            targetImageData.height,
          )

          // Draw processed image to canvas
          ctx.putImageData(newImageData, 0, 0)

          // Enable export button
          exportImageBtn.disabled = false

          alert('Encryption completed successfully!')
        } catch (err) {
          console.error('Encryption failed:', err)
        }
      }

      // Export canvas
      function exportCanvas() {
        try {
          const dataURL = canvas.toDataURL('image/png')
          const link = document.createElement('a')
          link.download = 'steganography-image.png'
          link.href = dataURL
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)

          alert('Image exported successfully!')
        } catch (err) {
          console.error('Failed to export image:', err)
        }
      }

      // Reset canvas
      function resetCanvas() {
        ctx.clearRect(0, 0, canvas.width, canvas.height)
        placeholder.style.display = 'block'
        targetImageData = null
        hiddenImageData = null
        hiddenDataBinary = null
        saveHiddenDataBtn.disabled = true
        startEncryptionBtn.disabled = true
        exportImageBtn.disabled = true
      }

      // Event listeners
      selectHiddenImageBtn.addEventListener('click', selectHiddenImage)
      saveHiddenDataBtn.addEventListener('click', saveHiddenImageData)
      selectTargetImageBtn.addEventListener('click', selectTargetImage)
      startEncryptionBtn.addEventListener('click', drawHiddenData)
      exportImageBtn.addEventListener('click', exportCanvas)
      resetCanvasBtn.addEventListener('click', resetCanvas)
    </script>
  </body>
</html>
